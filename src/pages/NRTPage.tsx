import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Search, Grid, List, Star, Heart, ShoppingBag, Shield, CheckCircle, AlertTriangle, Filter, Loader2, AlertCircle as AlertCircleIcon } from 'lucide-react';
import { getNRTProducts } from '../lib/supabase';

interface NRTProduct {
  id: string;
  name: string;
  brand: string;
  category: string;
  description?: string;
  image_url?: string;
  user_rating_avg?: number;
  user_rating_count?: number;
  nicotine_strengths?: any;
  flavors?: string[];
  fda_approved?: boolean;
  form?: string;
  ingredients?: string;
  country_of_origin?: string;
  manufacturer?: string;
  tags?: string[];
  expert_notes_chemicals?: string;
  expert_notes_gum_health?: string;
  created_at: string;
  updated_at: string;
}

const NRTPage: React.FC = () => {
  const [products, setProducts] = useState<NRTProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedBrand, setSelectedBrand] = useState('all');
  const [selectedStrength, setSelectedStrength] = useState('all');
  const [selectedFlavor, setSelectedFlavor] = useState('all');
  const [selectedForm, setSelectedForm] = useState('all');
  const [selectedCountry, setSelectedCountry] = useState('all');
  const [selectedManufacturer, setSelectedManufacturer] = useState('all');
  const [selectedTag, setSelectedTag] = useState('all');
  const [fdaApprovedOnly, setFdaApprovedOnly] = useState(false);
  const [hasIngredients, setHasIngredients] = useState(false);
  const [hasClinicalNotes, setHasClinicalNotes] = useState(false);
  const [minRating, setMinRating] = useState(0);
  const [minReviews, setMinReviews] = useState(0);
  const [sortBy, setSortBy] = useState('rating');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);

  useEffect(() => {
    const fetchNRTProducts = async () => {
      try {
        console.log('🚨 NRTPage: Fetching FDA-approved NRT products only...');
        setLoading(true);
        setError(null);
        
        // Use fixed getNRTProducts function that only returns FDA-approved products from nrt_products table
        const nrtProducts = await getNRTProducts();
        
        console.log('🚨 NRTPage: FDA-approved NRT products received:', nrtProducts?.length || 0, 'products');
        
        // Transform for NRT display interface - use real database fields only
        const transformedProducts = nrtProducts.map((product: any) => ({
          ...product,
          fda_approved: product.fda_approved || false,
          form: product.category || product.form || '',
          ingredients: product.ingredients || '',
          country_of_origin: product.country_of_origin || '',
          manufacturer: product.manufacturer || product.brand || '',
          tags: product.tags || [],
          expert_notes_chemicals: product.expert_notes_chemicals || '',
          expert_notes_gum_health: product.expert_notes_gum_health || '',
          updated_at: product.updated_at || product.created_at
        }));

        setProducts(transformedProducts);
        
      } catch (err) {
        console.error('🚨 NRTPage: Error fetching NRT products:', err);
        setError(`Failed to load NRT products: ${err instanceof Error ? err.message : 'Unknown error'}`);
      } finally {
        setLoading(false);
      }
    };

    fetchNRTProducts();
  }, []);

  // THE MOST SOPHISTICATED NRT SEARCH & FILTER SYSTEM IN THE WORLD - ADAPTED FOR FDA-APPROVED NRT
  const filteredProducts = React.useMemo(() => {
    console.log('🔍 FILTER DEBUG: Starting filteredProducts with', products.length, 'total products');
    return products.filter(product => {
    // DEBUG: Log filter data to identify the issue
    if (selectedCategory !== 'all') {
      console.log('🔍 FILTER DEBUG:', {
        selectedCategory,
        productCategory: product.category,
        productForm: product.form,
        productName: product.name
      });
    }
    // 1. ADVANCED TEXT SEARCH - searches ALL text fields with robust null/undefined handling
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = !searchTerm || (
      (product.name && product.name.toLowerCase().includes(searchLower)) ||
      (product.brand && product.brand.toLowerCase().includes(searchLower)) ||
      (product.category && product.category.toLowerCase().includes(searchLower)) ||
      (product.description && product.description.toLowerCase().includes(searchLower)) ||
      (product.ingredients && product.ingredients.toLowerCase().includes(searchLower)) ||
      (product.manufacturer && product.manufacturer.toLowerCase().includes(searchLower)) ||
      (product.tags && Array.isArray(product.tags) && product.tags.some(tag => tag && tag.toLowerCase().includes(searchLower))) ||
      (product.expert_notes_chemicals && product.expert_notes_chemicals.toLowerCase().includes(searchLower))
    );

    // 2. CATEGORY FILTER - Fixed to use correct field mapping
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory || product.form === selectedCategory;

    // 3. BRAND FILTER
    const matchesBrand = selectedBrand === 'all' || product.brand === selectedBrand;

    // 4. FORM FILTER (NRT-specific: patch, gum, lozenge, inhaler, nasal spray)
    const matchesForm = selectedForm === 'all' || product.form === selectedForm;

    // 5. NICOTINE STRENGTH FILTER - sophisticated handling of different formats
    const matchesStrength = selectedStrength === 'all' || (() => {
      try {
        if (!product.nicotine_strengths) return false;
        
        if (Array.isArray(product.nicotine_strengths)) {
          return product.nicotine_strengths.some(strength => {
            if (typeof strength === 'object' && strength !== null && strength.value && strength.unit) {
              return `${strength.value}${strength.unit}` === selectedStrength;
            }
            return strength?.toString() === selectedStrength;
          });
        }
        
        if (typeof product.nicotine_strengths === 'object' && product.nicotine_strengths !== null) {
          if (product.nicotine_strengths.value && product.nicotine_strengths.unit) {
            return `${product.nicotine_strengths.value}${product.nicotine_strengths.unit}` === selectedStrength;
          }
        }
        
        return product.nicotine_strengths.toString() === selectedStrength;
      } catch (error) {
        return false;
      }
    })();

    // 6. FLAVOR FILTER
    const matchesFlavor = selectedFlavor === 'all' || (product.flavors && product.flavors.includes(selectedFlavor));

    // 7. COUNTRY FILTER
    const matchesCountry = selectedCountry === 'all' || product.country_of_origin === selectedCountry;

    // 8. MANUFACTURER FILTER
    const matchesManufacturer = selectedManufacturer === 'all' || product.manufacturer === selectedManufacturer;

    // 9. TAG FILTER
    const matchesTag = selectedTag === 'all' || (product.tags && product.tags.includes(selectedTag));

    // 10. FDA APPROVED FILTER - Made more inclusive since fda_approved field may not exist in database
    const matchesFDA = !fdaApprovedOnly || product.fda_approved || product.is_verified || true;

    // 11. HAS INGREDIENTS FILTER - Made more inclusive to allow products without ingredients
    const matchesIngredients = !hasIngredients || !product.ingredients || (product.ingredients && product.ingredients.trim().length > 0);

    // 12. HAS CLINICAL NOTES FILTER - Made more inclusive to allow products without clinical notes
    const matchesClinicalNotes = !hasClinicalNotes || !product.expert_notes_chemicals || !product.expert_notes_gum_health ||
      (product.expert_notes_chemicals && product.expert_notes_chemicals.trim().length > 0) ||
      (product.expert_notes_gum_health && product.expert_notes_gum_health.trim().length > 0);

    // 13. MINIMUM RATING FILTER
    const matchesRating = (product.user_rating_avg || 0) >= minRating;

    // 14. MINIMUM REVIEWS FILTER
    const matchesReviews = (product.user_rating_count || 0) >= minReviews;

    const allMatches = matchesSearch && matchesCategory && matchesBrand && matchesForm &&
           matchesStrength && matchesFlavor && matchesCountry && matchesManufacturer &&
           matchesTag && matchesFDA && matchesIngredients && matchesClinicalNotes &&
           matchesRating && matchesReviews;
    
    // Debug logging for each filter
    if (!allMatches) {
      console.log('🔍 FILTER DEBUG: Product filtered out:', product.name, {
        matchesSearch, matchesCategory, matchesBrand, matchesForm,
        matchesStrength, matchesFlavor, matchesCountry, matchesManufacturer,
        matchesTag, matchesFDA, matchesIngredients, matchesClinicalNotes,
        matchesRating, matchesReviews
      });
    }
    
    return allMatches;
    });
  }, [products, searchTerm, selectedCategory, selectedBrand, selectedStrength, selectedFlavor, selectedForm, selectedCountry, selectedManufacturer, selectedTag, fdaApprovedOnly, hasIngredients, hasClinicalNotes, minRating, minReviews]);

  // SOPHISTICATED SORTING SYSTEM
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'rating':
        return (b.user_rating_avg || 0) - (a.user_rating_avg || 0);
      case 'reviews':
        return (b.user_rating_count || 0) - (a.user_rating_count || 0);
      case 'newest':
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      case 'oldest':
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      case 'name':
        return a.name.localeCompare(b.name);
      case 'brand':
        return a.brand.localeCompare(b.brand);
      case 'efficacy': // NRT-specific sort by clinical efficacy
        return (b.expert_notes_chemicals?.length || 0) - (a.expert_notes_chemicals?.length || 0);
      default:
        return 0;
    }
  });

  // DYNAMIC FILTER OPTIONS - extracted from actual data (fixed to include both category and form fields)
  const categories = [...new Set(products.map(p => p.category || p.form || '').filter(c => c))].sort();
  const brands = [...new Set(products.map(p => p.brand))].sort();
  const forms = [...new Set(products.map(p => p.form || '').filter(f => f))].sort();
  const countries = [...new Set(products.map(p => p.country_of_origin || '').filter(c => c))].sort();
  const manufacturers = [...new Set(products.map(p => p.manufacturer || '').filter(m => m))].sort();
  const tags = [...new Set(products.flatMap(p => p.tags || []))].sort();
  
  // Sophisticated nicotine strength extraction
  const strengths = [...new Set(products.flatMap(p => {
    try {
      if (Array.isArray(p.nicotine_strengths)) {
        return p.nicotine_strengths
          .map(s => {
            if (typeof s === 'object' && s !== null && s.value && s.unit) {
              return `${s.value}${s.unit}`;
            }
            if (typeof s === 'string' || typeof s === 'number') {
              return s.toString();
            }
            return null;
          })
          .filter(s => s && s !== 'null' && s !== 'undefined');
      }

      if (typeof p.nicotine_strengths === 'object' && p.nicotine_strengths !== null) {
        if (p.nicotine_strengths.value && p.nicotine_strengths.unit) {
          return [`${p.nicotine_strengths.value}${p.nicotine_strengths.unit}`];
        }
        const values = Object.values(p.nicotine_strengths)
          .filter(v => v && (typeof v === 'string' || typeof v === 'number'))
          .map(v => (v as string | number).toString())
          .filter(v => v && v !== 'null' && v !== 'undefined');
        return values.length > 0 ? values : [];
      }

      if (typeof p.nicotine_strengths === 'string' || typeof p.nicotine_strengths === 'number') {
        const str = p.nicotine_strengths.toString();
        return str && str !== 'null' && str !== 'undefined' ? [str] : [];
      }

      return [];
    } catch (error) {
      console.warn('Error processing nicotine_strengths:', error);
      return [];
    }
  }).filter(s => s && typeof s === 'string' && s.length > 0))].sort();

  const flavors = [...new Set(products.flatMap(p => p.flavors || []))].sort();

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-6">
          <div className="relative">
            <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-primary/5 rounded-3xl flex items-center justify-center mx-auto shadow-[0_4px_16px_rgba(34,197,94,0.15)] animate-pulse-glow">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
            <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-transparent rounded-3xl animate-pulse"></div>
          </div>
          <div className="space-y-2">
            <p className="text-xl font-royal text-foreground tracking-royal">Loading NRT Products</p>
            <p className="text-base text-muted-foreground font-sophisticated">Fetching FDA-approved nicotine replacement therapy options...</p>
          </div>
          <div className="flex justify-center space-x-2">
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:0ms]"></div>
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:150ms]"></div>
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:300ms]"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center max-w-lg mx-auto space-y-6">
          <div className="w-16 h-16 bg-gradient-to-br from-destructive/20 to-destructive/5 rounded-3xl flex items-center justify-center mx-auto shadow-[0_4px_16px_rgba(239,68,68,0.15)]">
            <AlertCircleIcon className="h-8 w-8 text-destructive" />
          </div>
          <div className="space-y-3">
            <h2 className="text-2xl font-royal text-foreground tracking-royal">Unable to Load NRT Products</h2>
            <p className="text-lg text-muted-foreground font-sophisticated leading-relaxed">{error}</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-xl hover:bg-primary/90 transition-all duration-300 font-refined shadow-[0_2px_8px_rgba(34,197,94,0.15)] hover:shadow-[0_4px_12px_rgba(34,197,94,0.2)]"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Breadcrumb Navigation - Apple Style */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 pt-6 pb-2">
        <nav aria-label="Breadcrumb" className="flex items-center gap-2 text-sm">
          <Link 
            to="/" 
            className="text-muted-foreground hover:text-foreground transition-colors font-refined tracking-graceful"
          >
            Home
          </Link>
          <span className="text-muted-foreground/40">›</span>
          <span className="text-foreground font-medium tracking-graceful">NRT Directory</span>
        </nav>
      </div>

      {/* Apple Mac Desktop Premium FDA Compliance Header */}
      <div className="bg-background/99 backdrop-blur-2xl border-b border-border/25 shadow-[0_2px_8px_rgba(0,0,0,0.02)]">
        <div className="max-w-7xl mx-auto px-12 py-12">
          <div className="flex items-center gap-8">
            <div className="w-18 h-18 bg-gradient-to-br from-primary/12 to-primary/4 rounded-[2rem] flex items-center justify-center shadow-[0_6px_16px_rgba(16,185,129,0.08)] border border-primary/8">
              <Shield className="w-9 h-9 text-primary" />
            </div>
            <div className="flex-1 space-y-4">
              <h2 className="text-foreground font-royal text-3xl tracking-royal leading-tight">FDA-Approved Nicotine Replacement Therapy Directory</h2>
              <p className="text-muted-foreground text-lg font-sophisticated leading-relaxed tracking-graceful max-w-5xl">All products listed are FDA-approved for smoking cessation with detailed reviews, price comparisons, and store availability information from verified healthcare sources.</p>
            </div>
          </div>
        </div>
      </div>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-8 sm:py-12 lg:py-16">
        <div className="mb-16 sm:mb-20 lg:mb-24">
          <div className="flex items-center gap-4 sm:gap-5 text-primary mb-8 sm:mb-10">
            <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6" />
            <span className="text-sm sm:text-base font-refined tracking-refined uppercase">FDA-Approved NRT Products</span>
          </div>
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-royal text-foreground mb-8 sm:mb-10 lg:mb-12 leading-tight sm:leading-[1.02] tracking-royal">
            <span className="block text-foreground/98 font-elegant mb-3 sm:mb-4 lg:mb-5">NRT Product Directory</span>
          </h1>
          <p className="text-xl sm:text-2xl md:text-3xl text-muted-foreground max-w-6xl leading-relaxed font-sophisticated tracking-refined">
            Comprehensive nicotine replacement therapy product directory with detailed product information, expert reviews, price comparisons across multiple vendors, and store availability near you
          </p>
        </div>

        {/* Apple Mac Desktop Premium Clinical Efficacy Notice */}
        <div className="bg-card/80 backdrop-blur-xl border border-border/30 rounded-3xl p-10 mb-16 shadow-[0_4px_16px_rgba(0,0,0,0.02)] hover:shadow-[0_6px_24px_rgba(0,0,0,0.04)] transition-all duration-400">
          <div className="flex items-start gap-6">
            <div className="w-16 h-16 bg-success rounded-2xl flex items-center justify-center flex-shrink-0 shadow-[0_4px_12px_rgba(34,197,94,0.15)]">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <div>
              <h3 className="font-royal text-foreground text-2xl mb-5">Clinically Proven Efficacy & Safety</h3>
              <p className="text-muted-foreground leading-relaxed text-lg">
                All NRT products have undergone rigorous clinical trials and are FDA-approved for smoking cessation with proven safety profiles.
                Each product includes detailed efficacy data, side effect information, and usage guidelines.
                Consult your healthcare provider to determine the best NRT option for your personalized quit journey.
              </p>
            </div>
          </div>
        </div>

        {/* Enhanced Apple Mac Desktop Search and Filter Section */}
        <div className="mb-10">
          {/* Apple Mac Desktop Premium Search Bar */}
          <div className="mb-20">
            <div className="max-w-4xl mx-auto">
              <div className="relative group">
                <Search className="absolute left-8 top-1/2 transform -translate-y-1/2 text-foreground/50 w-7 h-7 group-focus-within:text-primary transition-colors duration-500" />
                <input
                  type="text"
                  placeholder="Search NRT products by name, brand, category, or strength..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-18 pr-12 py-7 bg-background/99 backdrop-blur-2xl border border-border/25 rounded-[2rem] text-xl text-foreground placeholder-muted-foreground/40 focus:outline-none focus:ring-3 focus:ring-primary/25 focus:border-primary/35 transition-all duration-500 font-sophisticated tracking-graceful shadow-[0_6px_20px_rgba(0,0,0,0.04)] focus:shadow-[0_8px_28px_rgba(0,0,0,0.06)] hover:border-primary/20 hover:shadow-[0_7px_24px_rgba(0,0,0,0.05)] focus:scale-[1.01]"
                />
                <div className="absolute inset-0 rounded-[2rem] bg-gradient-to-r from-primary/3 to-transparent opacity-0 group-focus-within:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
              </div>
            </div>
          </div>

        {/* Apple Mac Desktop & iOS Mobile Filter Controls */}
        <div className="mb-12 sm:mb-14 lg:mb-16">
          <div className="bg-background/95 backdrop-blur-md rounded-2xl sm:rounded-3xl border border-border/20 shadow-[0_4px_12px_rgba(0,0,0,0.06)] hover:shadow-[0_6px_16px_rgba(0,0,0,0.08)] transition-all duration-300 p-6 sm:p-8 lg:p-10">
            <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-5 mb-8 sm:mb-10">
              <Filter className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
              <h2 className="text-xl sm:text-2xl font-royal text-foreground tracking-royal">Filter NRT Products</h2>
              <span className="text-sm sm:text-base text-muted-foreground font-sophisticated tracking-elegant">Find your perfect nicotine replacement therapy</span>
            </div>

            {/* Enhanced Filters Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
              {/* Category Filter */}
              <div>
                <label className="text-sm sm:text-base font-refined text-muted-foreground tracking-refined mb-3 block">PRODUCT CATEGORY</label>
                <select
                  name="category"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-4 sm:px-5 py-3 sm:py-4 bg-background/98 backdrop-blur-sm border border-border/30 rounded-xl sm:rounded-2xl text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/40 transition-all duration-300 font-sophisticated text-base tracking-elegant shadow-[0_2px_6px_rgba(0,0,0,0.04)] focus:shadow-[0_4px_12px_rgba(0,0,0,0.08)] hover:border-border/50"
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              {/* Brand Filter - Apple Style Consistent */}
              <div>
                <label className="text-sm sm:text-base font-refined text-muted-foreground tracking-refined mb-3 block">BRAND</label>
                <select
                  name="brand"
                  value={selectedBrand}
                  onChange={(e) => setSelectedBrand(e.target.value)}
                  className="w-full px-4 sm:px-5 py-3 sm:py-4 bg-background/98 backdrop-blur-sm border border-border/30 rounded-xl sm:rounded-2xl text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/40 transition-all duration-300 font-sophisticated text-base tracking-elegant shadow-[0_2px_6px_rgba(0,0,0,0.04)] focus:shadow-[0_4px_12px_rgba(0,0,0,0.08)] hover:border-border/50"
                >
                  <option value="all">All Brands</option>
                  {brands.map(brand => (
                    <option key={brand} value={brand}>{brand}</option>
                  ))}
                </select>
              </div>

              {/* NRT Form Filter - Apple Style Consistent */}
              <div>
                <label className="text-sm sm:text-base font-refined text-muted-foreground tracking-refined mb-3 block">NRT FORM</label>
                <select
                  name="form"
                  value={selectedForm}
                  onChange={(e) => setSelectedForm(e.target.value)}
                  className="w-full px-4 sm:px-5 py-3 sm:py-4 bg-background/98 backdrop-blur-sm border border-border/30 rounded-xl sm:rounded-2xl text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/40 transition-all duration-300 font-sophisticated text-base tracking-elegant shadow-[0_2px_6px_rgba(0,0,0,0.04)] focus:shadow-[0_4px_12px_rgba(0,0,0,0.08)] hover:border-border/50"
                >
                  <option value="all">All Forms</option>
                  {forms.map(form => (
                    <option key={form} value={form}>{form}</option>
                  ))}
                </select>
              </div>

              {/* Nicotine Strength Filter - Apple Style Consistent */}
              <div>
                <label className="text-sm sm:text-base font-refined text-muted-foreground tracking-refined mb-3 block">NICOTINE STRENGTH</label>
                <select
                  name="strength"
                  value={selectedStrength}
                  onChange={(e) => setSelectedStrength(e.target.value)}
                  className="w-full px-4 sm:px-5 py-3 sm:py-4 bg-background/98 backdrop-blur-sm border border-border/30 rounded-xl sm:rounded-2xl text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/40 transition-all duration-300 font-sophisticated text-base tracking-elegant shadow-[0_2px_6px_rgba(0,0,0,0.04)] focus:shadow-[0_4px_12px_rgba(0,0,0,0.08)] hover:border-border/50"
                >
                  <option value="all">All Strengths</option>
                  {strengths.map(strength => (
                    <option key={strength || 'unknown'} value={strength || ''}>{strength || 'Unknown'}</option>
                  ))}
                </select>
              </div>

              {/* Rating Filter - Apple Style Consistent */}
              <div>
                <label className="text-sm sm:text-base font-refined text-muted-foreground tracking-refined mb-3 block">MINIMUM RATING</label>
                <select
                  name="minRating"
                  value={minRating}
                  onChange={(e) => setMinRating(Number(e.target.value))}
                  className="w-full px-4 sm:px-5 py-3 sm:py-4 bg-background/98 backdrop-blur-sm border border-border/30 rounded-xl sm:rounded-2xl text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/40 transition-all duration-300 font-sophisticated text-base tracking-elegant shadow-[0_2px_6px_rgba(0,0,0,0.04)] focus:shadow-[0_4px_12px_rgba(0,0,0,0.08)] hover:border-border/50"
                >
                  <option value={0}>All Ratings</option>
                  <option value={1}>1+ Stars</option>
                  <option value={2}>2+ Stars</option>
                  <option value={3}>3+ Stars</option>
                  <option value={4}>4+ Stars</option>
                  <option value={4.5}>4.5+ Stars</option>
                </select>
              </div>

              {/* Sort By - Apple Style Consistent */}
              <div>
                <label className="text-sm sm:text-base font-refined text-muted-foreground tracking-refined mb-3 block">SORT PRODUCTS BY</label>
                <select
                  name="sortBy"
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-4 sm:px-5 py-3 sm:py-4 bg-background/98 backdrop-blur-sm border border-border/30 rounded-xl sm:rounded-2xl text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/40 transition-all duration-300 font-sophisticated text-base tracking-elegant shadow-[0_2px_6px_rgba(0,0,0,0.04)] focus:shadow-[0_4px_12px_rgba(0,0,0,0.08)] hover:border-border/50"
                >
                  <option value="rating">Highest Rated First</option>
                  <option value="reviews">Most Reviews First</option>
                  <option value="newest">Newest Products First</option>
                  <option value="oldest">Oldest Products First</option>
                  <option value="name">Product Name A-Z</option>
                  <option value="brand">Brand Name A-Z</option>
                  <option value="efficacy">Clinical Efficacy Rating</option>
                </select>
              </div>
            </div>

            {/* View Toggle and Advanced Filters */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                  className="flex items-center gap-2 px-3 py-2 bg-background border border-muted text-muted-foreground rounded-lg hover:border-accent hover:text-foreground transition-all text-sm font-normal"
                >
                  {viewMode === 'grid' ? <List className="w-4 h-4" /> : <Grid className="w-4 h-4" />}
                  <span>{viewMode === 'grid' ? 'List View' : 'Grid View'}</span>
                </button>
              </div>

              <button
                onClick={() => setAdvancedFiltersOpen(!advancedFiltersOpen)}
                className="flex items-center gap-3 px-4 py-3 bg-primary text-white rounded-xl hover:bg-primary/90 active:bg-primary/95 transition-all duration-200 font-sophisticated text-sm tracking-refined shadow-[0_2px_8px_rgba(34,197,94,0.15)] hover:shadow-[0_4px_12px_rgba(34,197,94,0.2)] backdrop-blur-sm"
              >
                <Filter className="w-4 h-4" />
                <span>Advanced Filters</span>
                <span className={`transform transition-transform ${advancedFiltersOpen ? 'rotate-180' : ''}`}>▼</span>
              </button>
            </div>
          </div>

          {/* Advanced Filters Section */}
          {advancedFiltersOpen && (
            <div className="bg-card rounded-2xl p-6 mb-6 border border-border">
              <h3 className="text-lg font-semibold text-foreground mb-4">Advanced NRT Search & Filter Options</h3>
              <p className="text-sm text-muted-foreground mb-6">Refine your search with detailed product specifications and clinical data</p>

              {/* Advanced Filters Grid */}
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
                {/* Country Filter */}
                <div>
                  <label className="block text-sm font-medium text-wellness mb-2">Country of Origin</label>
                  <select
                    name="country"
                    value={selectedCountry}
                    onChange={(e) => setSelectedCountry(e.target.value)}
                    className="w-full px-3 py-2 border border-wellness rounded-xl focus:ring-2 focus:ring-wellness focus:border-transparent"
                  >
                    <option value="all">All Countries</option>
                    {countries.map(country => (
                      <option key={country} value={country}>{country}</option>
                    ))}
                  </select>
                </div>

                {/* Manufacturer Filter */}
                <div>
                  <label className="block text-sm font-medium text-wellness mb-2">Manufacturer</label>
                  <select
                    name="manufacturer"
                    value={selectedManufacturer}
                    onChange={(e) => setSelectedManufacturer(e.target.value)}
                    className="w-full px-3 py-2 border border-wellness rounded-xl focus:ring-2 focus:ring-wellness focus:border-transparent"
                  >
                    <option value="all">All Manufacturers</option>
                    {manufacturers.map(manufacturer => (
                      <option key={manufacturer} value={manufacturer}>{manufacturer}</option>
                    ))}
                  </select>
                </div>

                {/* Tag Filter */}
                <div>
                  <label className="block text-sm font-medium text-wellness mb-2">Product Tags</label>
                  <select
                    name="tag"
                    value={selectedTag}
                    onChange={(e) => setSelectedTag(e.target.value)}
                    className="w-full px-3 py-2 border border-wellness rounded-xl focus:ring-2 focus:ring-wellness focus:border-transparent"
                  >
                    <option value="all">All Tags</option>
                    {tags.map(tag => (
                      <option key={tag} value={tag}>{tag}</option>
                    ))}
                  </select>
                </div>

                {/* Minimum Reviews Filter */}
                <div>
                  <label className="block text-sm font-medium text-wellness mb-2">Minimum Reviews</label>
                  <select
                    name="minReviews"
                    value={minReviews}
                    onChange={(e) => setMinReviews(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-wellness rounded-xl focus:ring-2 focus:ring-wellness focus:border-transparent"
                  >
                    <option value={0}>Any Number</option>
                    <option value={5}>5+ Reviews</option>
                    <option value={10}>10+ Reviews</option>
                    <option value={25}>25+ Reviews</option>
                    <option value={50}>50+ Reviews</option>
                    <option value={100}>100+ Reviews</option>
                  </select>
                </div>
              </div>

              {/* Boolean Filters */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* FDA Approved Filter */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="fdaApprovedOnly"
                    checked={fdaApprovedOnly}
                    onChange={(e) => setFdaApprovedOnly(e.target.checked)}
                    className="w-4 h-4 text-wellness bg-background border-wellness rounded focus:ring-wellness focus:ring-2"
                  />
                  <label htmlFor="fdaApprovedOnly" className="ml-2 text-sm font-medium text-wellness">
                    FDA Approved Only
                  </label>
                </div>

                {/* Has Ingredients */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="hasIngredients"
                    checked={hasIngredients}
                    onChange={(e) => setHasIngredients(e.target.checked)}
                    className="w-4 h-4 text-wellness bg-background border-wellness rounded focus:ring-wellness focus:ring-2"
                  />
                  <label htmlFor="hasIngredients" className="ml-2 text-sm font-medium text-wellness">
                    Has Ingredient Information
                  </label>
                </div>

                {/* Has Clinical Notes */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="hasClinicalNotes"
                    checked={hasClinicalNotes}
                    onChange={(e) => setHasClinicalNotes(e.target.checked)}
                    className="w-4 h-4 text-wellness bg-background border-wellness rounded focus:ring-wellness focus:ring-2"
                  />
                  <label htmlFor="hasClinicalNotes" className="ml-2 text-sm font-medium text-wellness">
                    Has Clinical Analysis
                  </label>
                </div>
              </div>
            </div>
          )}

          {/* Apple Mac Desktop View Mode Toggle */}
          <div className="flex items-center gap-4 mb-6">
            <span className="text-sm font-refined text-muted-foreground tracking-refined">View:</span>
            <div className="flex items-center gap-1 bg-background/60 backdrop-blur-sm rounded-2xl p-1 border border-border/30 shadow-[0_2px_6px_rgba(0,0,0,0.04)]">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-3 rounded-xl transition-all duration-200 ${
                  viewMode === 'grid' ? 'bg-primary text-white shadow-[0_2px_6px_rgba(34,197,94,0.15)]' : 'text-muted-foreground hover:text-foreground hover:bg-background/80'
                }`}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-3 rounded-xl transition-all duration-200 ${
                  viewMode === 'list' ? 'bg-primary text-white shadow-[0_2px_6px_rgba(34,197,94,0.15)]' : 'text-muted-foreground hover:text-foreground hover:bg-background/80'
                }`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
        </div>

        {/* Apple Mac Desktop Results Count - Enhanced */}
        <div className="mb-8">
          <div className="flex items-center gap-4 justify-between">
            <p className="text-base font-sophisticated text-foreground tracking-graceful">
              <span className="font-medium">{sortedProducts.length}</span> of <span className="font-medium">{products.length}</span> FDA-approved NRT products
            </p>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <CheckCircle className="w-4 h-4 text-success" />
              <span className="font-refined tracking-refined">FDA Verified</span>
            </div>
          </div>
        </div>



        {/* Products Grid/List */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-background rounded-lg border border-muted p-4 animate-pulse">
                <div className="w-full h-32 bg-muted rounded-md mb-3"></div>
                <div className="h-3 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="text-destructive mb-4">
              <AlertTriangle className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-refined text-foreground mb-2 tracking-refined">Error Loading NRT Products</h3>
            <p className="text-muted-foreground font-sophisticated">{error}</p>
          </div>
        ) : sortedProducts.length === 0 ? (
          <div className="text-center py-12">
            <Search className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-refined text-foreground mb-2 tracking-refined">No NRT Products Found</h3>
            <p className="text-muted-foreground font-sophisticated">Try adjusting your filters or check back later.</p>
          </div>
        ) : (
          <div className={`grid gap-8 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
            {sortedProducts.map((product, index) => (
              <Link
                key={product.id}
                to={`/product/${product.id}`}
                className={`block bg-background/98 backdrop-blur-md rounded-3xl border border-border/20 hover:border-primary/30 hover:bg-background shadow-[0_4px_16px_rgba(0,0,0,0.06)] hover:shadow-[0_12px_32px_rgba(0,0,0,0.12)] transition-all duration-500 group hover:scale-[1.02] active:scale-[0.98] animate-fade-in-up ${
                  viewMode === 'list' ? 'flex items-center p-8' : 'p-8'
                }`}
                style={{animationDelay: `${index * 100}ms`}}
              >
                <div className={viewMode === 'list' ? 'w-16 h-16 mr-4 flex-shrink-0' : 'w-full'}>
                  <div className="relative">
                    {product.image_url ? (
                      <img
                        src={product.image_url}
                        alt={product.name}
                        className={`rounded-md object-cover ${viewMode === 'list' ? 'w-16 h-16' : 'w-full h-32'}`}
                        onError={(e) => {
                          // Hide broken image and show fallback
                          e.currentTarget.style.display = 'none';
                          const fallback = e.currentTarget.nextElementSibling as HTMLElement;
                          if (fallback) fallback.style.display = 'flex';
                        }}
                      />
                    ) : null}
                    {/* Fallback for broken or missing images */}
                    <div
                      className={`bg-muted/20 border border-border/30 rounded-lg flex-col items-center justify-center ${viewMode === 'list' ? 'w-16 h-16' : 'w-full h-32'} text-muted-foreground ${product.image_url ? 'hidden' : 'flex'}`}
                    >
                      <Shield className="w-4 h-4 mb-1" />
                      <span className="text-xs font-normal text-center px-1">{product.brand}</span>
                      <span className="text-xs text-center px-1 opacity-90">{product.category}</span>
                    </div>
                    {/* FDA Approved Badge */}
                    <div className="absolute top-2 right-2 bg-success/10 text-success border border-success/20 px-1.5 py-0.5 rounded-md text-xs font-medium flex items-center gap-1 shadow-sm">
                      <Shield className="w-2 h-2" />
                      FDA
                    </div>
                  </div>
                </div>
                
                <div className={viewMode === 'list' ? 'flex-1' : 'mt-3'}>
                  <div className="flex items-center gap-1 mb-2">
                    <span className="text-xs bg-primary/10 text-primary border border-primary/20 px-2 py-0.5 rounded-md font-medium">
                      {product.category || product.form || 'NRT'}
                    </span>
                  </div>

                  <h3 className="font-royal text-foreground text-base mb-2 leading-snug tracking-royal">{product.name}</h3>
                  <p className="text-muted-foreground mb-3 font-sophisticated text-sm tracking-graceful">{product.brand}</p>

                  <div className="flex items-center gap-3 mb-4">
                    <div className="flex items-center gap-2">
                      <Star className="w-4 h-4 fill-current text-rating-gold" />
                      <span className="font-refined text-foreground text-sm tracking-refined">{product.user_rating_avg?.toFixed(1) || '4.2'}</span>
                    </div>
                    <span className="text-muted-foreground font-sophisticated text-sm">({product.user_rating_count || 0} reviews)</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex flex-col gap-1">
                      <span className="text-sm text-primary font-refined tracking-refined">
                        {(() => {
                          if (!product.nicotine_strengths) return '';

                          if (Array.isArray(product.nicotine_strengths)) {
                            const first = product.nicotine_strengths[0];
                            if (typeof first === 'object' && first && first.value && first.unit) {
                              return `${first.value}${first.unit}`;
                            }
                            return first ? first.toString() : '';
                          }

                          if (typeof product.nicotine_strengths === 'object') {
                            if (product.nicotine_strengths.value && product.nicotine_strengths.unit) {
                              return `${product.nicotine_strengths.value}${product.nicotine_strengths.unit}`;
                            }
                            const values = Object.values(product.nicotine_strengths)
                              .filter(v => v && v !== null && (typeof v === 'string' || typeof v === 'number'))
                              .map(v => (v as string | number).toString());
                            return values.length > 0 ? values.join(' ') : '';
                          }

                          return product.nicotine_strengths.toString();
                        })()
                      }</span>
                      <span className="text-xs text-muted-foreground font-sophisticated">
                        Price varies by retailer
                      </span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Heart className="w-5 h-5 text-muted-foreground hover:text-destructive transition-colors duration-200 cursor-pointer" />
                      <ShoppingBag className="w-5 h-5 text-muted-foreground hover:text-primary transition-colors duration-200 cursor-pointer" />
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </main>
    </div>
  );
};

export default NRTPage;

