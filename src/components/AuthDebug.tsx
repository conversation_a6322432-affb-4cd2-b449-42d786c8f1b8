import { useEffect, useState } from 'react';
import { supabase, missionFreshSupabase } from '../lib/supabase';

export const AuthDebug = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    const checkAuth = async () => {
      console.log('🚨 AUTH DEBUG: Starting comprehensive auth check');
      
      try {
        // Test 1: Check Supabase client
        console.log('🚨 AUTH DEBUG: Testing Supabase client...');
        console.log('Supabase client configured');
        
        // Test 2: Check current session
        console.log('🚨 AUTH DEBUG: Checking current session...');
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
        console.log('Session data:', sessionData);
        console.log('Session error:', sessionError);
        
        // Test 3: Check user
        console.log('🚨 AUTH DEBUG: Checking current user...');
        const { data: userData, error: userError } = await supabase.auth.getUser();
        console.log('User data:', userData);
        console.log('User error:', userError);
        
        // Test 4: Test database connection
        console.log('🚨 AUTH DEBUG: Testing database connection...');
        const { data: dbTest, error: dbError } = await missionFreshSupabase
          .from('user_progress')
          .select('*')
          .limit(1);
        console.log('DB test result:', dbTest);
        console.log('DB test error:', dbError);
        
        // Test 5: Test sign in
        console.log('🚨 AUTH DEBUG: Testing sign in...');
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: '<EMAIL>',
          password: 'TestPassword123!'
        });
        console.log('Sign in data:', signInData);
        console.log('Sign in error:', signInError);
        
        setDebugInfo({
          supabaseConfigured: true,
          sessionData,
          sessionError,
          userData,
          userError,
          dbTest,
          dbError,
          signInData,
          signInError
        });
        
      } catch (error) {
        console.error('🚨 AUTH DEBUG: Critical error:', error);
        setDebugInfo({ criticalError: (error as Error).message });
      }
    };

    checkAuth();
  }, []);

  return (
    <div style={{ 
      position: 'fixed', 
      top: 10, 
      right: 10, 
      background: 'white', 
      border: '2px solid red', 
      padding: 10, 
      fontSize: 12, 
      maxWidth: 300, 
      maxHeight: 400, 
      overflow: 'auto',
      zIndex: 9999
    }}>
      <h3>Auth Debug Info</h3>
      <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
    </div>
  );
};
